#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to inspect current database table structure
Use this when you have tables that aren't in models.py
"""

import sqlalchemy as sa
from sqlalchemy import create_engine, MetaData, Table
from onyx.configs.app_configs import POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_HOST, POSTGRES_PORT, POSTGRES_DB

def get_table_structure(table_name: str):
    """Get the current structure of a table from the database"""
    
    # Create connection string
    connection_string = f"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"
    
    # Create engine and reflect the table
    engine = create_engine(connection_string)
    metadata = MetaData()
    
    try:
        # Reflect the specific table
        table = Table(table_name, metadata, autoload_with=engine)
        
        print(f"\n=== Table: {table_name} ===")
        print("Columns:")
        for column in table.columns:
            nullable = "nullable=True" if column.nullable else "nullable=False"
            default = f", server_default='{column.server_default}'" if column.server_default else ""
            primary_key = ", primary_key=True" if column.primary_key else ""
            
            print(f"  sa.Column('{column.name}', {column.type}, {nullable}{default}{primary_key}),")
        
        print("\nConstraints:")
        for constraint in table.constraints:
            if hasattr(constraint, 'columns'):
                cols = [col.name for col in constraint.columns]
                if constraint.name and 'pkey' in constraint.name:
                    print(f"  sa.PrimaryKeyConstraint({', '.join(repr(c) for c in cols)}),")
                elif constraint.name and 'key' in constraint.name:
                    print(f"  sa.UniqueConstraint({', '.join(repr(c) for c in cols)}),")
        
        print("\nForeign Keys:")
        for fk in table.foreign_keys:
            print(f"  sa.ForeignKeyConstraint(['{fk.parent.name}'], ['{fk.column.table.name}.{fk.column.name}']),")
            
    except Exception as e:
        print(f"Error inspecting table {table_name}: {e}")
    finally:
        engine.dispose()

def list_all_tables():
    """List all tables in the database"""
    connection_string = f"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"
    engine = create_engine(connection_string)
    metadata = MetaData()
    
    try:
        metadata.reflect(bind=engine)
        print("All tables in database:")
        for table_name in metadata.tables.keys():
            print(f"  - {table_name}")
    except Exception as e:
        print(f"Error listing tables: {e}")
    finally:
        engine.dispose()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python inspect_db_structure.py <table_name>  # Inspect specific table")
        print("  python inspect_db_structure.py --list       # List all tables")
        sys.exit(1)
    
    if sys.argv[1] == "--list":
        list_all_tables()
    else:
        table_name = sys.argv[1]
        get_table_structure(table_name)
