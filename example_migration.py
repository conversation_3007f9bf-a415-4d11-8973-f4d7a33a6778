"""Drop and recreate EE tables for CE cleanup

Revision ID: your_revision_id
Revises: previous_revision
Create Date: 2025-01-30 12:00:00.000000
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'your_revision_id'
down_revision = 'previous_revision'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Example: Drop and recreate user_group table

    # Step 1: Backup data (optional)
    op.execute("""
        CREATE TEMP TABLE user_group_backup AS
        SELECT * FROM user_group
    """)

    # Step 2: Drop the table
    op.drop_table('user_group')

    # Step 3: Recreate the table with identical structure
    # Converted from UserGroup model in models.py
    op.create_table(
        'user_group',
        # id: Mapped[int] = mapped_column(primary_key=True)
        sa.Column('id', sa.Integer(), nullable=False),

        # name: Mapped[str] = mapped_column(String, unique=True)
        sa.Column('name', sa.String(), nullable=False),

        # is_up_to_date: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
        sa.Column('is_up_to_date', sa.Boolean(), nullable=False, server_default=sa.text('false')),

        # is_up_for_deletion: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
        sa.Column('is_up_for_deletion', sa.Boolean(), nullable=False, server_default=sa.text('false')),

        # time_last_modified_by_user: Mapped[datetime.datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
        sa.Column('time_last_modified_by_user', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),

        # Constraints
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )

    # Step 4: Restore data (optional)
    op.execute("""
        INSERT INTO user_group
        SELECT * FROM user_group_backup
    """)


def downgrade() -> None:
    # This would reverse the process if needed
    pass
