import os
import sys

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from onyx.utils.encryption import encrypt_string_to_bytes, decrypt_bytes_to_string, test_encryption

# Set an encryption key for testing
os.environ["ENCRYPTION_KEY_SECRET"] = "test-encryption-key-for-community-edition"

def main():
    print("Testing encryption and decryption...")
    
    # Test using the test_encryption function
    try:
        test_encryption()
        print("✅ Basic encryption test passed")
    except Exception as e:
        print(f"❌ Basic encryption test failed: {e}")
        return False
    
    # Test with various strings
    test_strings = [
        "Simple string",
        "String with special characters: !@#$%^&*()",
        "String with unicode characters: 你好, こんにちは, 안녕하세요",
        "Very long string: " + "a" * 1000,
        "",  # Empty string
    ]
    
    for i, test_string in enumerate(test_strings):
        try:
            encrypted = encrypt_string_to_bytes(test_string)
            decrypted = decrypt_bytes_to_string(encrypted)
            
            if decrypted != test_string:
                print(f"❌ Test {i+1} failed: Decrypted string does not match original")
                return False
            
            print(f"✅ Test {i+1} passed")
        except Exception as e:
            print(f"❌ Test {i+1} failed with exception: {e}")
            return False
    
    print("All encryption tests passed!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)