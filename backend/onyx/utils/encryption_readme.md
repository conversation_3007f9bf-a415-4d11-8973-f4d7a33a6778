# Encryption in Onyx Community Edition

This document explains how to set up and use encryption for sensitive data in Onyx Community Edition.

## Overview

Onyx now supports encryption of sensitive data such as:
- LLM provider API keys
- Connector credentials
- Other sensitive configuration data

The encryption implementation uses AES in CBC mode with PKCS7 padding, which is a secure and widely-used encryption method.

## Setup

To enable encryption, you need to set the `ENCRYPTION_KEY_SECRET` environment variable:

```bash
export ENCRYPTION_KEY_SECRET="your-secure-encryption-key"
```

### Requirements for the Encryption Key

- The key must be at least 16 characters long
- For optimal security, use a random string of at least 32 characters
- Valid key lengths are 16, 24, or 32 bytes (characters)
- If a key of a different length is provided, it will be trimmed to the nearest valid length

### Example of generating a secure key

You can generate a secure random key using the following command:

```bash
openssl rand -base64 32
```

## How Encryption Works

When encryption is enabled (by setting the `ENCRYPTION_KEY_SECRET` environment variable):

1. Sensitive data is encrypted before being stored in the database
2. The data is automatically decrypted when retrieved from the database
3. The encryption is transparent to the application code

If `<PERSON>NCRYP<PERSON>ON_KEY_SECRET` is not set, the system will fall back to storing data unencrypted, but will log a warning.

## Important Notes

- **Changing the encryption key**: If you change the encryption key after data has been encrypted, you will not be able to decrypt the existing data. Make sure to back up your encryption key securely.
- **Backward compatibility**: The system is designed to handle both encrypted and unencrypted data. If decryption fails (e.g., due to a key change), it will attempt to treat the data as unencrypted.
- **Security**: Store your encryption key securely and separately from your database backups.

## Testing Encryption

You can test that encryption is working correctly by running the provided test script:

```bash
cd backend
export ENCRYPTION_KEY_SECRET="your-test-key"
python scripts/test_encryption.py
```

## Troubleshooting

If you encounter issues with encryption:

1. Verify that the `ENCRYPTION_KEY_SECRET` environment variable is set correctly
2. Check that the key meets the length requirements (16, 24, or 32 bytes)
3. If you're migrating from unencrypted data, ensure that the system can handle both encrypted and unencrypted data

For further assistance, please refer to the Onyx documentation or contact support.